# -*- coding: utf-8 -*-
"""
Typethink OpenAI API 代理服务器

这是一个将 Typethink API 转换为 OpenAI 兼容格式的代理服务器。
它提供了标准的 OpenAI API 端点，同时在后台使用 Typethink 的聊天完成服务。

主要功能：
- 提供 OpenAI 兼容的 API 接口
- 支持流式和非流式响应
- 客户端 API 密钥认证
- Typethink 令牌轮换机制
- 模型列表管理
"""

import json
import os
import time
import uuid
import threading
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx
from fastapi import FastAPI, HTTPException, Depends
from fastapi.responses import StreamingResponse
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

# 配置常量
TYPETHINK_API_URL = "https://chat.typethink.ai/api/chat/completions"  # Typethink API 端点

# 全局变量：用于存储客户端 API 密钥和 Typethink 令牌
VALID_CLIENT_KEYS: set = set()  # 有效的客户端 API 密钥集合
TYPETHINK_TOKENS: list = [eyJpZCI6IjJkNGQwZjAxLWU3YzAtNDRhMS1hMWVmLTcxMzZiYzZiZWIxMCIsImV4cCI6MTc0ODcwNzk4Mn0.mPAGAuwvrvuF3YTUQ1PDrJsQ_4e9D-haxpPbjpKO7XY]  # Typethink 令牌列表
current_typethink_token_index: int = 0  # 当前使用的令牌索引
token_rotation_lock = threading.Lock()  # 令牌轮换的线程锁


# Pydantic 数据模型定义
class ChatMessage(BaseModel):
    """聊天消息模型

    用于表示单条聊天消息，包含角色和内容
    """
    role: str  # 消息角色：user、assistant 或 system
    content: str  # 消息内容


class ChatCompletionRequest(BaseModel):
    """聊天完成请求模型

    定义客户端发送的聊天完成请求的结构
    """
    model: str  # 要使用的模型名称
    messages: List[ChatMessage]  # 聊天消息列表
    stream: bool = False  # 是否启用流式响应
    temperature: Optional[float] = None  # 温度参数，控制随机性
    max_tokens: Optional[int] = None  # 最大生成令牌数
    top_p: Optional[float] = None  # 核采样参数


class ModelInfo(BaseModel):
    """模型信息模型

    表示单个可用模型的信息
    """
    id: str  # 模型唯一标识符
    object: str = "model"  # 对象类型，固定为 "model"
    created: int  # 模型创建时间戳
    owned_by: str  # 模型所有者


class ModelList(BaseModel):
    """模型列表模型

    包含所有可用模型的列表
    """
    object: str = "list"  # 对象类型，固定为 "list"
    data: List[ModelInfo]  # 模型信息列表


class ChatCompletionChoice(BaseModel):
    """聊天完成选择模型

    表示聊天完成响应中的一个选择项
    """
    message: ChatMessage  # 生成的消息
    index: int = 0  # 选择项索引
    finish_reason: str = "stop"  # 完成原因


class ChatCompletionResponse(BaseModel):
    """聊天完成响应模型

    非流式聊天完成的完整响应
    """
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")  # 响应唯一标识符
    object: str = "chat.completion"  # 对象类型
    created: int = Field(default_factory=lambda: int(time.time()))  # 创建时间戳
    model: str  # 使用的模型名称
    choices: List[ChatCompletionChoice]  # 生成的选择列表
    usage: Dict[str, int] = Field(
        default_factory=lambda: {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0})  # 令牌使用统计


class StreamChoice(BaseModel):
    """流式响应选择模型

    表示流式响应中的单个数据块
    """
    delta: Dict[str, Any] = Field(default_factory=dict)  # 增量数据
    index: int = 0  # 选择项索引
    finish_reason: Optional[str] = None  # 完成原因（如果有）


class StreamResponse(BaseModel):
    """流式响应模型

    流式聊天完成的单个响应块
    """
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")  # 响应唯一标识符
    object: str = "chat.completion.chunk"  # 对象类型
    created: int = Field(default_factory=lambda: int(time.time()))  # 创建时间戳
    model: str  # 使用的模型名称
    choices: List[StreamChoice]  # 流式选择列表


# FastAPI 应用实例
app = FastAPI(title="Typethink OpenAI API")  # 创建 FastAPI 应用
security = HTTPBearer(auto_error=False)  # HTTP Bearer 认证方案

# 全局变量
models_data = {}  # 存储从配置文件加载的模型数据


def load_models():
    """从 models.json 文件加载模型配置

    Returns:
        dict: 包含模型数据的字典，如果加载失败则返回空数据结构
    """
    try:
        with open("models.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading models.json: {e}")
        return {"data": []}


def load_client_api_keys():
    """从 client_api_keys.json 文件加载客户端 API 密钥

    更新全局变量 VALID_CLIENT_KEYS，用于客户端认证
    """
    global VALID_CLIENT_KEYS
    try:
        with open("client_api_keys.json", "r", encoding="utf-8") as f:
            keys = json.load(f)
            # 验证文件格式是否正确（应该是列表）
            if not isinstance(keys, list):
                print("Warning: client_api_keys.json should contain a list of keys.")
                VALID_CLIENT_KEYS = set()
                return
            VALID_CLIENT_KEYS = set(keys)  # 转换为集合以提高查找效率
            if not VALID_CLIENT_KEYS:
                print("Warning: client_api_keys.json is empty.")
            else:
                print(f"Successfully loaded {len(VALID_CLIENT_KEYS)} client API keys.")
    except FileNotFoundError:
        print("Error: client_api_keys.json not found.")
        VALID_CLIENT_KEYS = set()
    except Exception as e:
        print(f"Error loading client_api_keys.json: {e}")
        VALID_CLIENT_KEYS = set()


def load_typethink_tokens():
    """从 accounts.json 文件加载 Typethink 令牌

    解析账户配置文件，提取 websocket_cookies 中的 token 字段
    更新全局变量 TYPETHINK_TOKENS
    """
    global TYPETHINK_TOKENS
    try:
        with open("accounts.json", "r", encoding="utf-8") as f:
            accounts_data = json.load(f)
            # 验证文件格式是否正确（应该是列表）
            if not isinstance(accounts_data, list):
                print("Warning: accounts.json should contain a list of accounts.")
                TYPETHINK_TOKENS = []
                return

            loaded_tokens = []
            # 遍历每个账户，提取有效的令牌
            for account in accounts_data:
                token = account.get("websocket_cookies", {}).get("token")
                if token and isinstance(token, str):
                    loaded_tokens.append(token)

            TYPETHINK_TOKENS = loaded_tokens
            if not TYPETHINK_TOKENS:
                print("Warning: No valid tokens found in accounts.json.")
            else:
                print(f"Successfully loaded {len(TYPETHINK_TOKENS)} Typethink tokens.")

    except FileNotFoundError:
        print("Error: accounts.json not found.")
        TYPETHINK_TOKENS = []
    except Exception as e:
        print(f"Error loading accounts.json: {e}")
        TYPETHINK_TOKENS = []


def get_model_item(model_id: str) -> Optional[Dict]:
    """根据模型 ID 从已加载的模型数据中获取模型项

    Args:
        model_id: 要查找的模型标识符

    Returns:
        Optional[Dict]: 匹配的模型数据字典，如果未找到则返回 None
    """
    for model in models_data.get("data", []):
        if model.get("id") == model_id:
            return model
    return None


async def authenticate_client(auth: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """基于 Authorization 头中的 API 密钥进行客户端认证

    Args:
        auth: HTTP Bearer 认证凭据

    Raises:
        HTTPException: 当认证失败时抛出相应的 HTTP 异常
    """
    # 检查是否配置了客户端 API 密钥
    if not VALID_CLIENT_KEYS:
        raise HTTPException(status_code=503, detail="Service unavailable: No client API keys configured.")

    # 检查是否提供了认证信息
    if not auth or not auth.credentials:
        raise HTTPException(
            status_code=401,
            detail="API key required in Authorization header.",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 验证 API 密钥是否有效
    if auth.credentials not in VALID_CLIENT_KEYS:
        raise HTTPException(status_code=403, detail="Invalid client API key.")


def get_next_typethink_token() -> str:
    """使用轮询算法获取下一个 Typethink 令牌

    实现负载均衡，确保所有可用令牌都能被使用

    Returns:
        str: 要使用的 Typethink 令牌

    Raises:
        HTTPException: 当没有可用令牌时抛出异常
    """
    global current_typethink_token_index

    # 检查是否有可用的令牌
    if not TYPETHINK_TOKENS:
        raise HTTPException(status_code=503, detail="Service unavailable: No Typethink tokens configured.")

    # 使用线程锁确保令牌轮换的线程安全
    with token_rotation_lock:
        if not TYPETHINK_TOKENS:
            raise HTTPException(status_code=503, detail="Service unavailable: Typethink tokens unavailable.")
        token_to_use = TYPETHINK_TOKENS[current_typethink_token_index]
        # 更新索引，实现轮询
        current_typethink_token_index = (current_typethink_token_index + 1) % len(TYPETHINK_TOKENS)
    return token_to_use


@app.on_event("startup")
async def startup():
    """应用启动时的初始化函数

    加载所有必要的配置文件和数据
    """
    global models_data
    models_data = load_models()  # 加载模型配置
    load_client_api_keys()  # 加载客户端 API 密钥
    load_typethink_tokens()  # 加载 Typethink 令牌


@app.get("/v1/models", response_model=ModelList)
async def list_models(_: None = Depends(authenticate_client)):
    """列出所有可用的模型

    提供 OpenAI 兼容的模型列表端点

    Returns:
        ModelList: 包含所有可用模型信息的列表
    """
    model_list = []
    # 遍历已加载的模型数据，构建模型信息列表
    for model in models_data.get("data", []):
        model_list.append(ModelInfo(
            id=model.get("id", ""),
            created=model.get("created", int(time.time())),
            owned_by=model.get("owned_by", "typethink")
        ))
    return ModelList(data=model_list)


@app.post("/v1/chat/completions")
async def chat_completions(
        request: ChatCompletionRequest,
        _: None = Depends(authenticate_client)
):
    """创建聊天完成

    提供 OpenAI 兼容的聊天完成端点，支持流式和非流式响应

    Args:
        request: 聊天完成请求对象
        _: 客户端认证依赖项

    Returns:
        StreamingResponse 或 ChatCompletionResponse: 根据请求类型返回相应的响应

    Raises:
        HTTPException: 当模型不存在时抛出 404 异常
    """
    # 验证请求的模型是否存在
    model_item = get_model_item(request.model)
    if not model_item:
        raise HTTPException(status_code=404, detail=f"Model {request.model} not found")

    # 获取下一个可用的 Typethink 令牌
    typethink_token = get_next_typethink_token()

    # 构建发送给 Typethink API 的请求载荷
    payload = {
        "stream": True,  # 始终使用流式请求（内部处理）
        "model": request.model,
        "messages": [{"role": msg.role, "content": msg.content} for msg in request.messages],
        "params": {},  # 模型参数
        "tool_servers": [],  # 工具服务器（暂未使用）
        "features": {  # 功能开关
            "image_generation": False,
            "code_interpreter": False,
            "web_search": False,
        },
        "model_item": model_item,  # 模型详细信息
        "background_tasks": {"title_generation": False, "tags_generation": False},  # 后台任务配置
    }

    # 添加可选的模型参数
    if request.temperature is not None:
        payload["params"]["temperature"] = request.temperature
    if request.max_tokens is not None:
        payload["params"]["max_tokens"] = request.max_tokens
    if request.top_p is not None:
        payload["params"]["top_p"] = request.top_p

    # 构建请求头，模拟浏览器请求
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Content-Type": "application/json",
        "sec-ch-ua-platform": '"Windows"',
        "authorization": f"Bearer {typethink_token}",  # 使用 Typethink 令牌进行认证
        "origin": "https://chat.typethink.ai",
    }

    # 根据客户端请求类型返回相应的响应
    if request.stream:
        # 返回流式响应
        return StreamingResponse(
            stream_generator(payload, headers, request.model),
            media_type="text/event-stream"
        )
    else:
        # 返回非流式响应
        return await non_stream_response(payload, headers, request.model)


async def stream_generator(payload: Dict, headers: Dict, model: str) -> AsyncGenerator[str, None]:
    """生成流式响应数据

    将 Typethink API 的流式响应转换为 OpenAI 兼容格式

    Args:
        payload: 发送给 Typethink API 的请求载荷
        headers: HTTP 请求头
        model: 模型名称

    Yields:
        str: 格式化的 Server-Sent Events 数据
    """
    try:
        # 创建异步 HTTP 客户端，无超时限制
        async with httpx.AsyncClient(timeout=None) as client:
            # 发起流式 POST 请求
            async with client.stream("POST", TYPETHINK_API_URL, json=payload, headers=headers) as response:
                # 检查响应状态码
                if response.status_code != 200:
                    error_msg = await response.aread()
                    yield f'data: {{"error": "API Error: {error_msg.decode()}"}}\n\n'
                    yield "data: [DONE]\n\n"
                    return

                # 逐行处理流式响应
                async for line in response.aiter_lines():
                    if not line:
                        continue

                    # 处理 Server-Sent Events 格式的数据
                    if line.startswith("data: "):
                        data = line[6:].strip()  # 移除 "data: " 前缀
                        if data == "[DONE]":
                            yield "data: [DONE]\n\n"
                            break

                        try:
                            event = json.loads(data)
                            # 转换为 OpenAI 格式
                            delta = {}
                            finish_reason = None

                            # 提取响应数据
                            if event.get("choices") and len(event["choices"]) > 0:
                                choice = event["choices"][0]
                                if choice.get("delta"):
                                    delta = choice["delta"]
                                if choice.get("finish_reason"):
                                    finish_reason = choice["finish_reason"]

                            # 构建 OpenAI 兼容的流式响应
                            stream_resp = StreamResponse(
                                model=model,
                                choices=[StreamChoice(delta=delta, finish_reason=finish_reason)]
                            )
                            yield f"data: {stream_resp.json()}\n\n"

                        except json.JSONDecodeError:
                            # 忽略无效的 JSON 数据
                            continue
                        except Exception as e:
                            print(f"Stream error: {e}")
                            continue
    except Exception as e:
        # 处理连接或其他异常
        yield f'data: {{"error": "Stream error: {str(e)}"}}\n\n'
        yield "data: [DONE]\n\n"


async def non_stream_response(payload: Dict, headers: Dict, model: str) -> ChatCompletionResponse:
    """生成非流式响应

    收集完整的流式响应数据，组装成单个完整的响应对象

    Args:
        payload: 发送给 Typethink API 的请求载荷
        headers: HTTP 请求头
        model: 模型名称

    Returns:
        ChatCompletionResponse: 完整的聊天完成响应

    Raises:
        HTTPException: 当 API 调用失败或发生内部错误时抛出异常
    """
    content_parts = []  # 存储响应内容的各个部分

    try:
        # 创建异步 HTTP 客户端，设置 60 秒超时
        async with httpx.AsyncClient(timeout=60) as client:
            # 发起流式请求以获取完整响应
            async with client.stream("POST", TYPETHINK_API_URL, json=payload, headers=headers) as response:
                # 检查响应状态码
                if response.status_code != 200:
                    error_msg = await response.aread()
                    raise HTTPException(status_code=response.status_code, detail=f"API Error: {error_msg.decode()}")

                # 逐行处理响应，收集内容
                async for line in response.aiter_lines():
                    if not line or not line.startswith("data: "):
                        continue

                    data = line[6:].strip()  # 移除 "data: " 前缀
                    if data == "[DONE]":
                        break

                    try:
                        event = json.loads(data)
                        # 提取内容增量
                        if event.get("choices") and len(event["choices"]) > 0:
                            choice = event["choices"][0]
                            if choice.get("delta") and choice["delta"].get("content"):
                                content_parts.append(choice["delta"]["content"])
                    except json.JSONDecodeError:
                        # 忽略无效的 JSON 数据
                        continue

        # 将所有内容部分合并为完整响应
        full_content = "".join(content_parts)
        return ChatCompletionResponse(
            model=model,
            choices=[ChatCompletionChoice(
                message=ChatMessage(role="assistant", content=full_content)
            )]
        )

    except httpx.HTTPStatusError as e:
        # 处理上游 API 错误
        raise HTTPException(status_code=e.response.status_code, detail=f"Upstream error: {e.response.text}")
    except Exception as e:
        # 处理其他内部错误
        raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")


if __name__ == "__main__":
    """主程序入口点

    当脚本直接运行时执行以下操作：
    1. 检查并创建默认的模型配置文件
    2. 启动 FastAPI 服务器
    """
    import uvicorn

    # 如果 models.json 文件不存在，创建一个默认的模型配置
    if not os.path.exists("models.json"):
        dummy_models = {
            "data": [
                {
                    "id": "us.anthropic.claude-sonnet-4-20250514-v1:0",
                    "object": "model",
                    "created": int(time.time()),
                    "owned_by": "anthropic",
                    "name": "Claude Sonnet 4",
                    "openai": {  # OpenAI 兼容性配置
                        "id": "us.anthropic.claude-sonnet-4-20250514-v1:0",
                        "object": "model",
                        "created": int(time.time()),
                        "owned_by": "openai",
                        "connection_type": "external"
                    },
                    "info": {  # 模型详细信息
                        "id": "us.anthropic.claude-sonnet-4-20250514-v1:0",
                        "name": "Claude Sonnet 4",
                        "meta": {"capabilities": {"vision": True}},  # 模型能力
                        "is_active": True
                    }
                }
            ]
        }
        # 写入默认配置文件
        with open("models.json", "w", encoding="utf-8") as f:
            json.dump(dummy_models, f, indent=2)

    # 打印服务器启动信息
    print("Starting Typethink OpenAI API server...")
    print("Endpoints:")
    print("  GET  /v1/models")
    print("  POST /v1/chat/completions")
    print("\nUse client API keys (sk-xxx) in Authorization header")

    # 启动 uvicorn 服务器，监听所有接口的 8000 端口
    uvicorn.run(app, host="0.0.0.0", port=8000)